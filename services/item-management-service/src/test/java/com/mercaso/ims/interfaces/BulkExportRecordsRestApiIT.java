package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.service.BulkExportRecordsApplicationService;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

class BulkExportRecordsRestApiIT extends AbstractIT {

    // BulkExportRecordsApplicationService MockBean is now defined in AbstractIT

    @Test
    void shouldSuccessWhenGetBulkExportRecordsFile() throws Exception {
        // Arrange
        UUID recordId = UUID.randomUUID();
        DocumentResponse mockResponse = new DocumentResponse();
        mockResponse.setName("test-export.xlsx");
        mockResponse.setSignedUrl("https://example.com/test-export.xlsx");
        
        when(bulkExportRecordsApplicationService.getBulkExportRecordsFile(any(UUID.class)))
            .thenReturn(mockResponse);

        // Act & Assert
        DocumentResponse result = bulkExportRecordsRestApiUtil.getBulkExportRecordsFile(recordId);

        assertNotNull(result);
        assertNotNull(result.getName());
        assertNotNull(result.getSignedUrl());
    }
}