package com.mercaso.ims.interfaces;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.service.BulkExportApplicationService;
import com.mercaso.ims.domain.bulkexportrecords.enums.ExportType;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

class BulkExportRestApiIT extends AbstractIT {

    // BulkExportApplicationService MockBean is now defined in AbstractIT

    @Test
    void shouldSuccessWhenDownloadFilteredBulkReportWithAllParams() throws Exception {
        // Arrange
        String customFilter = "brand:TestBrand";
        ExportType exportType = ExportType.ITEM;

        doNothing().when(bulkExportApplicationService).getBulkReportFile(anyString(), any(ExportType.class));

        // Act
        bulkExportRestApiUtil.downloadFilteredBulkReport(customFilter, exportType);

        // Assert
        verify(bulkExportApplicationService).getBulkReportFile(customFilter, exportType);
    }

    @Test
    void shouldSuccessWhenDownloadFilteredBulkReportWithCustomFilterOnly() throws Exception {
        // Arrange
        String customFilter = "category:Electronics";
        ExportType exportType = ExportType.ITEM;

        doNothing().when(bulkExportApplicationService).getBulkReportFile(anyString(), any());

        // Act
        bulkExportRestApiUtil.downloadFilteredBulkReport(customFilter, null);

        // Assert
        verify(bulkExportApplicationService).getBulkReportFile(customFilter, exportType);
    }

    @Test
    void shouldSuccessWhenDownloadFilteredBulkReportWithExportTypeOnly() throws Exception {
        // Arrange
        ExportType exportType = ExportType.SUPPLIER;

        doNothing().when(bulkExportApplicationService).getBulkReportFile(any(), any(ExportType.class));

        // Act
        bulkExportRestApiUtil.downloadFilteredBulkReport(null, exportType);

        // Assert
        verify(bulkExportApplicationService).getBulkReportFile(null, exportType);
    }

    @Test
    void shouldSuccessWhenDownloadFilteredBulkReportWithAllExportType() throws Exception {
        // Arrange
        String customFilter = "vvendor:TestVendor";
        ExportType exportType = ExportType.ALL;

        doNothing().when(bulkExportApplicationService).getBulkReportFile(anyString(), any(ExportType.class));

        // Act
        bulkExportRestApiUtil.downloadFilteredBulkReport(customFilter, exportType);

        // Assert
        verify(bulkExportApplicationService).getBulkReportFile(customFilter, exportType);
    }

    @Test
    void shouldSuccessWhenDownloadFilteredBulkReportWithoutParams() throws Exception {
        ExportType exportType = ExportType.ITEM;
        // Arrange
        doNothing().when(bulkExportApplicationService).getBulkReportFile(any(), any());

        // Act
        bulkExportRestApiUtil.downloadFilteredBulkReportWithoutParams();

        // Assert
        verify(bulkExportApplicationService).getBulkReportFile(null, exportType);
    }
}