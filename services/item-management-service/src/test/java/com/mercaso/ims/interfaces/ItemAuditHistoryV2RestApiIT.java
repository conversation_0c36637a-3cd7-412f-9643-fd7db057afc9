package com.mercaso.ims.interfaces;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.mercaso.ims.AbstractIT;
import com.mercaso.ims.application.command.CreateItemCommand;
import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.application.dto.ItemAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.service.ItemVersionApplicationService;
import com.mercaso.ims.domain.brand.Brand;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetail;
import com.mercaso.ims.domain.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailFactory;
import com.mercaso.ims.utils.brand.BrandUtil;
import com.mercaso.ims.utils.item.ItemCommandUtil;
import com.mercaso.ims.utils.itemadjustmentrequestdetail.ItemAdjustmentRequestDetailCommandUtil;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;


class ItemAuditHistoryV2RestApiIT extends AbstractIT {

    // ItemVersionApplicationService MockBean is now defined in AbstractIT

    @Test
    void shouldSuccessGetCreatedItemAuditHistoryList() throws Exception {

        ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailFactory.create(ItemAdjustmentRequestDetailCommandUtil.buildCreateItemAdjustmentRequestDetailCommand(
            UUID.randomUUID()));
        detail = itemAdjustmentRequestDetailRepository.save(detail);
        CreateItemCommand createItemCommand = ItemCommandUtil.buildCreateItemCommand(detail.getId());

        Brand brand = BrandUtil.buildBrand(createItemCommand.getBrandId());
        Brand save = brandService.save(brand);
        createItemCommand.setBrandId(save.getId());

        ItemDto itemDto = itemApplicationService.create(createItemCommand);

        List<ItemAuditHistoryInfoDto> itemAuditHistoryInfoDtos = itemAuditHistoryV2RestApiUtil.itemAuditHistoryListRequest(itemDto.getId());

        assertNotNull(itemAuditHistoryInfoDtos);
        assertEquals(1, itemAuditHistoryInfoDtos.size());
    }


    @Test
    void shouldSuccessGetUpdatedItemAuditHistoryList() throws Exception {
        ItemAdjustmentRequestDetail detail = ItemAdjustmentRequestDetailFactory.create(ItemAdjustmentRequestDetailCommandUtil.buildCreateItemAdjustmentRequestDetailCommand(
            UUID.randomUUID()));
        detail = itemAdjustmentRequestDetailRepository.save(detail);

        CreateItemCommand createItemCommand = ItemCommandUtil.buildCreateItemCommand(detail.getId());

        Brand save = brandService.save(BrandUtil.buildBrand(createItemCommand.getBrandId()));
        createItemCommand.setBrandId(save.getId());

        ItemDto create = itemApplicationService.create(createItemCommand);

        UpdateItemCommand updateItemCommand = ItemCommandUtil.buildUpdateItemCommand(create.getId(),
            create.getSkuNumber(),
            detail.getId());
        ItemDto update = itemApplicationService.update(updateItemCommand);

        List<ItemAuditHistoryInfoDto> itemAuditHistoryDtos = itemAuditHistoryV2RestApiUtil.itemAuditHistoryListRequest(update.getId());

        assertNotNull(itemAuditHistoryDtos);
        assertEquals(2, itemAuditHistoryDtos.size());
    }
}
